<!DOCTYPE html>
<html>
<head>
    <title>Animated Story with "Going To"</title>
    <style>
        body {
            font-family: 'Open Dyslexic', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #e74c3c;
        }
        .story-area {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }
        .story-text {
            font-size: 20px;
            margin-bottom: 20px;
            min-height: 100px;
        }
        .animation-area {
            height: 200px;
            background-color: #eaf7fd;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
        }
        .character {
            position: absolute;
            width: 80px;
            height: 80px;
            bottom: 20px;
            left: 50px;
            transition: all 1s ease;
        }
        .options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        .option {
            background-color: #d6eaf8;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            text-align: center;
            font-size: 18px;
            min-width: 120px;
            transition: all 0.3s;
        }
        .option:hover {
            background-color: #aed6f1;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .dyslexia-friendly {
            letter-spacing: 0.05em;
            word-spacing: 0.1em;
        }
        .highlight {
            background-color: #f9e79f;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .story-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-friendly">
        <h1>Create Your "Going To" Story</h1>
        
        <div class="story-controls">
            <button id="prevBtn" disabled>Previous</button>
            <button id="nextBtn">Next</button>
            <button id="playBtn">Play Story</button>
        </div>
        
        <div class="story-area">
            <div class="story-text" id="storyText">
                Click "Next" to start building your story!
            </div>
            
            <div class="animation-area" id="animationArea">
                <div class="character" id="character">
                    <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                        <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                        <circle cx="40" cy="35" r="3" fill="#000"/>
                        <circle cx="60" cy="35" r="3" fill="#000"/>
                        <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="options" id="options">
            <!-- Options will appear here -->
        </div>
    </div>

    <script>
        const storyParts = [
            {
                prompt: "Choose a character for your story:",
                options: [
                    { text: "Emma", pronoun: "She", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                        <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                        <circle cx="40" cy="35" r="3" fill="#000"/>
                        <circle cx="60" cy="35" r="3" fill="#000"/>
                        <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>` },
                    { text: "Tom", pronoun: "He", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="40" r="20" fill="#D4A5A5"/>
                        <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                        <circle cx="40" cy="35" r="3" fill="#000"/>
                        <circle cx="60" cy="35" r="3" fill="#000"/>
                        <path d="M40,45 Q50,55 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>` },
                    { text: "Katie", pronoun: "She", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="40" r="20" fill="#FFAAA5"/>
                        <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                        <circle cx="40" cy="35" r="3" fill="#000"/>
                        <circle cx="60" cy="35" r="3" fill="#000"/>
                        <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>` },
                    { text: "I", pronoun: "I", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="40" r="20" fill="#A5D6A7"/>
                        <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                        <circle cx="40" cy="35" r="3" fill="#000"/>
                        <circle cx="60" cy="35" r="3" fill="#000"/>
                        <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>` }
                ],
                getText: (choice) => `Our story is about ${choice.text}.`
            },
            {
                prompt: "What is your character going to do on Monday?",
                options: [
                    { text: "visit the zoo", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="60" width="60" height="20" fill="#8D6E63"/>
                        <rect x="25" y="40" width="10" height="20" fill="#795548"/>
                        <rect x="65" y="40" width="10" height="20" fill="#795548"/>
                        <circle cx="40" cy="30" r="10" fill="#FFD54F"/>
                        <circle cx="60" cy="30" r="10" fill="#4FC3F7"/>
                    </svg>` },
                    { text: "ride a bike", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="30" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                        <circle cx="70" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                        <path d="M30,55 L50,30 L70,55" stroke="#000" stroke-width="3" fill="none"/>
                        <line x1="50" y1="30" x2="50" y2="55" stroke="#000" stroke-width="3"/>
                    </svg>` },
                    { text: "play football", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="50" cy="50" r="20" fill="#FFF" stroke="#000" stroke-width="2"/>
                        <path d="M50,30 L50,70 M30,50 L70,50 M40,40 L60,60 M40,60 L60,40" stroke="#000" stroke-width="2"/>
                    </svg>` }
                ],
                getText: (choice, story) => {
                    const pronoun = story[0].choice.pronoun;
                    const verb = pronoun === 'I' ? 'am' : choice.verb;
                    return `On Monday, ${pronoun} ${verb} going to ${choice.text}.`;
                }
            },
            {
                prompt: "What is your character going to do on Tuesday?",
                options: [
                    { text: "help dad", verb: "is", possessive: "his", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="35" cy="40" r="15" fill="#FFD3B6"/>
                        <circle cx="65" cy="40" r="15" fill="#D4A5A5"/>
                        <path d="M35,55 L35,80 M25,65 L45,65" stroke="#000" stroke-width="2" fill="none"/>
                        <path d="M65,55 L65,80 M55,65 L75,65" stroke="#000" stroke-width="2" fill="none"/>
                        <path d="M35,50 L50,30 L65,50" stroke="#000" stroke-width="2" fill="none"/>
                    </svg>` },
                    { text: "watch TV", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="60" height="40" fill="#424242"/>
                        <rect x="35" y="70" width="30" height="5" fill="#757575"/>
                        <circle cx="30" cy="25" r="5" fill="#E0E0E0"/>
                    </svg>` },
                    { text: "visit family", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="30" cy="50" r="15" fill="#FFD3B6"/>
                        <circle cx="50" cy="50" r="15" fill="#D4A5A5"/>
                        <circle cx="70" cy="50" r="15" fill="#FFAAA5"/>
                    </svg>` }
                ],
                getText: (choice, story) => {
                    const pronoun = story[0].choice.pronoun;
                    const verb = pronoun === 'I' ? 'am' : choice.verb;
                    return `On Tuesday, ${pronoun} ${verb} going to ${choice.text}.`;
                }
            },
            {
                prompt: "What is your character going to take with them?",
                options: [
                    { text: "a camera", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="20" y="30" width="60" height="40" rx="5" fill="#555"/>
                        <circle cx="50" cy="50" r="15" fill="#222"/>
                        <circle cx="50" cy="50" r="8" fill="#000"/>
                        <rect x="65" y="25" width="10" height="10" fill="#555"/>
                    </svg>` },
                    { text: "some sun cream", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="30" y="40" width="40" height="50" rx="5" fill="#FFD700"/>
                        <circle cx="50" cy="30" r="15" fill="#FFA500"/>
                    </svg>` },
                    { text: "a hairbrush", verb: "is", svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <rect x="40" y="30" width="10" height="50" fill="#8B4513"/>
                        <line x1="40" y1="30" x2="20" y2="15" stroke="#8B4513" stroke-width="3"/>
                        <line x1="45" y1="30" x2="25" y2="15" stroke="#8B4513" stroke-width="3"/>
                        <line x1="50" y1="30" x2="30" y2="15" stroke="#8B4513" stroke-width="3"/>
                    </svg>` }
                ],
                getText: (choice, story) => {
                    const pronoun = story[0].choice.pronoun;
                    const verb = pronoun === 'I' ? 'am' : choice.verb;
                    return `${pronoun} ${verb} going to take ${choice.text}.`;
                }
            }
        ];
        
        let currentPart = 0;
        let storyChoices = [];
        
        // Initialize options
        function showOptions() {
            const optionsContainer = document.getElementById('options');
            optionsContainer.innerHTML = '';
            
            const part = storyParts[currentPart];
            document.getElementById('storyText').textContent = part.prompt;
            
            part.options.forEach((option, index) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.textContent = option.text;
                optionDiv.addEventListener('click', () => selectOption(option));
                optionsContainer.appendChild(optionDiv);
            });
            
            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentPart === 0;
            document.getElementById('nextBtn').disabled = currentPart === storyParts.length - 1;
        }
        
        // Select option
        function selectOption(option) {
            if (storyChoices[currentPart]) {
                storyChoices[currentPart].choice = option;
            } else {
                storyChoices.push({ choice: option });
            }
            
            // Update character SVG if it's the first part
            if (currentPart === 0) {
                document.getElementById('character').innerHTML = option.svg;
            }
            
            // Show the generated text
            const generatedText = storyParts[currentPart].getText(option, storyChoices);
            document.getElementById('storyText').innerHTML = `
                <p>${storyParts[currentPart].prompt}</p>
                <p style="color:#2980b9;margin-top:10px;">${generatedText}</p>
            `;
        }
        
        // Next part
        document.getElementById('nextBtn').addEventListener('click', function() {
            if (currentPart < storyParts.length - 1) {
                currentPart++;
                showOptions();
            }
        });
        
        // Previous part
        document.getElementById('prevBtn').addEventListener('click', function() {
            if (currentPart > 0) {
                currentPart--;
                showOptions();
                
                // If we have a choice for this part, show it
                if (storyChoices[currentPart]) {
                    const generatedText = storyParts[currentPart].getText(
                        storyChoices[currentPart].choice, 
                        storyChoices
                    );
                    document.getElementById('storyText').innerHTML = `
                        <p>${storyParts[currentPart].prompt}</p>
                        <p style="color:#2980b9;margin-top:10px;">${generatedText}</p>
                    `;
                }
            }
        });
        
        // Play story
        document.getElementById('playBtn').addEventListener('click', function() {
            if (storyChoices.length < storyParts.length) {
                alert("Please complete all parts of the story first!");
                return;
            }
            
            const storyText = [
                `This is a story about ${storyChoices[0].choice.text}.`,
                storyParts[1].getText(storyChoices[1].choice, storyChoices),
                storyParts[2].getText(storyChoices[2].choice, storyChoices),
                storyParts[3].getText(storyChoices[3].choice, storyChoices),
                `What a great week ${storyChoices[0].choice.text} is going to have!`
            ];
            
            let currentLine = 0;
            const animationArea = document.getElementById('animationArea');
            const character = document.getElementById('character');
            
            // Reset character position
            character.style.left = '50px';
            
            function animateStory() {
                if (currentLine >= storyText.length) {
                    document.getElementById('storyText').innerHTML = storyText.join('<br><br>');
                    return;
                }
                
                document.getElementById('storyText').textContent = storyText[currentLine];
                
                // Animate character based on the current line
                if (currentLine === 1) {
                    // Monday activity
                    if (storyChoices[1].choice.text.includes('zoo')) {
                        character.style.left = '200px';
                    } else if (storyChoices[1].choice.text.includes('bike')) {
                        character.style.left = '400px';
                        character.innerHTML = `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                            <circle cx="70" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                            <path d="M30,55 L50,30 L70,55" stroke="#000" stroke-width="3" fill="none"/>
                            <line x1="50" y1="30" x2="50" y2="55" stroke="#000" stroke-width="3"/>
                            <circle cx="50" cy="30" r="10" fill="${storyChoices[0].choice.svg.match(/fill="([^"]+)"/)[1]}"/>
                        </svg>`;
                    } else {
                        // Football
                        character.style.left = '600px';
                        character.innerHTML = `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="50" cy="50" r="20" fill="#FFF" stroke="#000" stroke-width="2"/>
                            <path d="M50,30 L50,70 M30,50 L70,50 M40,40 L60,60 M40,60 L60,40" stroke="#000" stroke-width="2"/>
                            <circle cx="50" cy="50" r="10" fill="${storyChoices[0].choice.svg.match(/fill="([^"]+)"/)[1]}"/>
                        </svg>`;
                    }
                } else if (currentLine === 2) {
                    // Tuesday activity
                    if (storyChoices[2].choice.text.includes('help')) {
                        character.style.left = '300px';
                        character.innerHTML = storyChoices[0].choice.svg;
                    } else if (storyChoices[2].choice.text.includes('TV')) {
                        character.style.left = '500px';
                        character.innerHTML = `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <rect x="20" y="30" width="60" height="40" fill="#424242"/>
                            <rect x="35" y="70" width="30" height="5" fill="#757575"/>
                            <circle cx="30" cy="25" r="5" fill="#E0E0E0"/>
                            <circle cx="50" cy="50" r="10" fill="${storyChoices[0].choice.svg.match(/fill="([^"]+)"/)[1]}"/>
                        </svg>`;
                    } else {
                        // Visit family
                        character.style.left = '100px';
                        character.innerHTML = `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="30" cy="50" r="15" fill="#FFD3B6"/>
                            <circle cx="50" cy="50" r="15" fill="#D4A5A5"/>
                            <circle cx="70" cy="50" r="15" fill="#FFAAA5"/>
                            <circle cx="50" cy="50" r="10" fill="${storyChoices[0].choice.svg.match(/fill="([^"]+)"/)[1]}"/>
                        </svg>`;
                    }
                }
                
                currentLine++;
                setTimeout(animateStory, 3000);
            }
            
            animateStory();
        });
        
        // Start the game
        showOptions();
    </script>
</body>
</html>