<!DOCTYPE html>
<html>
<head>
    <title>What are you going to take?</title>
    <style>
        body {
            font-family: 'Comic Sans MS', sans-serif;
            background-color: #f0f9ff;
            color: #2b5876;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .people {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }
        .person {
            background-color: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
            width: 120px;
        }
        .person img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
        }
        .items {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }
        .item {
            background-color: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: grab;
            text-align: center;
            width: 80px;
            font-size: 14px;
        }
        .item img {
            width: 50px;
            height: 50px;
            margin-bottom: 5px;
        }
        .suitcase {
            border: 3px dashed #4a90e2;
            border-radius: 10px;
            min-height: 150px;
            width: 80%;
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            background-color: rgba(255,255,255,0.7);
        }
        .feedback {
            margin-top: 20px;
            font-size: 18px;
            min-height: 30px;
            color: #e74c3c;
        }
        .correct {
            color: #27ae60;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        }
        button:hover {
            background-color: #357abd;
        }
        .dyslexia-friendly {
            line-height: 1.6;
            letter-spacing: 0.05em;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-friendly">
        <h1>What are they going to take?</h1>
        
        <div class="people">
            <div class="person" data-person="Emma">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Emma</p>
            </div>
            <div class="person" data-person="Tom">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#D4A5A5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,55 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Tom</p>
            </div>
            <div class="person" data-person="Katie">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFAAA5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Katie</p>
            </div>
        </div>
        
        <div class="items">
            <div class="item" draggable="true" data-item="a camera">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="30" width="60" height="40" rx="5" fill="#555"/>
                    <circle cx="50" cy="50" r="15" fill="#222"/>
                    <circle cx="50" cy="50" r="8" fill="#000"/>
                    <rect x="65" y="25" width="10" height="10" fill="#555"/>
                </svg>
                <p>a camera</p>
            </div>
            <div class="item" draggable="true" data-item="some sun cream">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="30" y="40" width="40" height="50" rx="5" fill="#FFD700"/>
                    <circle cx="50" cy="30" r="15" fill="#FFA500"/>
                </svg>
                <p>some sun cream</p>
            </div>
            <div class="item" draggable="true" data-item="a bowl">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <path d="M30,50 Q50,80 70,50 L65,40 Q50,70 35,40 Z" fill="#A0522D"/>
                </svg>
                <p>a bowl</p>
            </div>
            <div class="item" draggable="true" data-item="a hat">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <ellipse cx="50" cy="40" rx="30" ry="15" fill="#FF6347"/>
                    <rect x="35" y="40" width="30" height="20" fill="#FF6347"/>
                </svg>
                <p>a hat</p>
            </div>
            <div class="item" draggable="true" data-item="a hairbrush">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="40" y="30" width="10" height="50" fill="#8B4513"/>
                    <line x1="40" y1="30" x2="20" y2="15" stroke="#8B4513" stroke-width="3"/>
                    <line x1="45" y1="30" x2="25" y2="15" stroke="#8B4513" stroke-width="3"/>
                    <line x1="50" y1="30" x2="30" y2="15" stroke="#8B4513" stroke-width="3"/>
                </svg>
                <p>a hairbrush</p>
            </div>
        </div>
        
        <h2>Emma's suitcase:</h2>
        <div class="suitcase" id="suitcase">
            <!-- Dropped items will appear here -->
        </div>
        
        <div class="feedback" id="feedback"></div>
        
        <button id="checkBtn">Check Answer</button>
    </div>

    <script>
        let selectedPerson = 'Emma';
        const correctItems = {
            'Emma': ['a camera', 'a hairbrush'],
            'Tom': ['some sun cream', 'a hat'],
            'Katie': ['a bowl', 'a hairbrush']
        };
        
        // Select person
        document.querySelectorAll('.person').forEach(person => {
            person.addEventListener('click', function() {
                document.querySelectorAll('.person').forEach(p => p.style.backgroundColor = 'white');
                this.style.backgroundColor = '#e3f2fd';
                selectedPerson = this.getAttribute('data-person');
                document.querySelector('h2').textContent = `${selectedPerson}'s suitcase:`;
                document.getElementById('feedback').textContent = '';
                document.getElementById('suitcase').innerHTML = '';
            });
        });
        
        // Make items draggable
        document.querySelectorAll('.item').forEach(item => {
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.getAttribute('data-item'));
            });
        });
        
        // Allow drop in suitcase
        const suitcase = document.getElementById('suitcase');
        suitcase.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        
        suitcase.addEventListener('drop', function(e) {
            e.preventDefault();
            const item = e.dataTransfer.getData('text/plain');
            const newItem = document.createElement('div');
            newItem.className = 'item';
            newItem.setAttribute('data-item', item);
            newItem.textContent = item;
            newItem.draggable = true;
            newItem.addEventListener('dragstart', function(ev) {
                ev.dataTransfer.setData('text/plain', this.getAttribute('data-item'));
            });
            this.appendChild(newItem);
        });
        
        // Check answer
        document.getElementById('checkBtn').addEventListener('click', function() {
            const itemsInSuitcase = Array.from(suitcase.children).map(item => item.getAttribute('data-item'));
            const correct = correctItems[selectedPerson];
            
            if (itemsInSuitcase.length === 0) {
                document.getElementById('feedback').textContent = 'Please add some items to the suitcase!';
                return;
            }
            
            const allCorrect = itemsInSuitcase.every(item => correct.includes(item)) && 
                              correct.every(item => itemsInSuitcase.includes(item));
            
            const feedback = document.getElementById('feedback');
            if (allCorrect) {
                feedback.textContent = `Correct! ${selectedPerson} is going to take ${formatItems(correct)}.`;
                feedback.className = 'feedback correct';
                
                // Create sentence
                const sentence = document.createElement('div');
                sentence.className = 'sentence';
                sentence.textContent = `Is ${selectedPerson} going to take ${formatItems(correct)}? Yes, ${getPronoun(selectedPerson)} is.`;
                sentence.style.marginTop = '20px';
                sentence.style.fontSize = '18px';
                sentence.style.color = '#2b5876';
                feedback.appendChild(sentence);
            } else {
                feedback.textContent = `Try again! ${selectedPerson} needs different items.`;
                feedback.className = 'feedback';
            }
        });
        
        function formatItems(items) {
            if (items.length === 1) return items[0];
            return items.slice(0, -1).join(', ') + ' and ' + items[items.length - 1];
        }
        
        function getPronoun(name) {
            return name === 'Tom' ? 'he' : 'she';
        }
    </script>
</body>
</html>