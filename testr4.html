<!DOCTYPE html>
<html>
<head>
    <title>Going To Questions Game</title>
    <style>
        body {
            font-family: 'Open Dyslexic', sans-serif;
            background-color: #e8f4f8;
            color: #2c3e50;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #e74c3c;
        }
        .game-area {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .question {
            font-size: 22px;
            text-align: center;
            margin-bottom: 20px;
            min-height: 60px;
            color: #2980b9;
        }
        .options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        .option {
            background-color: #d6eaf8;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            text-align: center;
            font-size: 18px;
            min-width: 120px;
            transition: all 0.3s;
        }
        .option:hover {
            background-color: #aed6f1;
        }
        .option.correct {
            background-color: #a3e4d7;
        }
        .option.incorrect {
            background-color: #f5b7b1;
        }
        .feedback {
            text-align: center;
            font-size: 20px;
            min-height: 30px;
            margin: 15px 0;
        }
        .correct-feedback {
            color: #27ae60;
        }
        .incorrect-feedback {
            color: #e74c3c;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        .score {
            text-align: center;
            font-size: 20px;
            margin-bottom: 20px;
        }
        .character {
            text-align: center;
            margin: 20px 0;
        }
        .character svg {
            width: 100px;
            height: 100px;
        }
        .dyslexia-friendly {
            letter-spacing: 0.05em;
            word-spacing: 0.1em;
        }
        .highlight {
            background-color: #f9e79f;
            padding: 2px 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-friendly">
        <h1>Question Time with "Going To"</h1>
        
        <div class="score">
            Score: <span id="scoreValue">0</span> / <span id="totalQuestions">0</span>
        </div>
        
        <div class="game-area">
            <div class="character" id="character">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
            </div>
            
            <div class="question" id="question">
                Click "Start Game" to begin!
            </div>
            
            <div class="options" id="options">
                <!-- Options will appear here -->
            </div>
            
            <div class="feedback" id="feedback"></div>
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Game</button>
            <button id="nextBtn" style="display:none;">Next Question</button>
        </div>
    </div>

    <script>
        const questions = [
            {
                question: "Is Emma going to take a camera?",
                character: "Emma",
                correctAnswer: "Yes, she is.",
                options: ["Yes, she is.", "No, she isn't.", "Yes, he is.", "No, they aren't."],
                svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            },
            {
                question: "Is Tom going to take some sun cream?",
                character: "Tom",
                correctAnswer: "No, he isn't.",
                options: ["Yes, she is.", "No, he isn't.", "Yes, they are.", "No, we aren't."],
                svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#D4A5A5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,55 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            },
            {
                question: "Are you going to visit the zoo on Monday?",
                character: "You",
                correctAnswer: "Yes, I am.",
                options: ["Yes, I am.", "No, I'm not.", "Yes, she is.", "No, they aren't."],
                svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#A5D6A7"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            },
            {
                question: "Is Katie going to take a hairbrush?",
                character: "Katie",
                correctAnswer: "Yes, she is.",
                options: ["Yes, she is.", "No, she isn't.", "Yes, he is.", "No, it isn't."],
                svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFAAA5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            },
            {
                question: "Are Billy and Lucy going to play football?",
                character: "Billy and Lucy",
                correctAnswer: "Yes, they are.",
                options: ["Yes, they are.", "No, they aren't.", "Yes, we are.", "No, he isn't."],
                svg: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="35" cy="40" r="15" fill="#FFD3B6"/>
                    <circle cx="65" cy="40" r="15" fill="#D4A5A5"/>
                    <path d="M35,55 L35,80 M25,65 L45,65" stroke="#000" stroke-width="2" fill="none"/>
                    <path d="M65,55 L65,80 M55,65 L75,65" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            }
        ];
        
        let currentQuestion = 0;
        let score = 0;
        let gameStarted = false;
        
        // Start game
        document.getElementById('startBtn').addEventListener('click', function() {
            gameStarted = true;
            this.style.display = 'none';
            document.getElementById('nextBtn').style.display = 'inline-block';
            document.getElementById('feedback').textContent = '';
            showQuestion();
        });
        
        // Next question
        document.getElementById('nextBtn').addEventListener('click', function() {
            document.getElementById('feedback').textContent = '';
            currentQuestion++;
            if (currentQuestion < questions.length) {
                showQuestion();
            } else {
                // Game over
                document.getElementById('question').innerHTML = `Game Over! Your score is <span class="highlight">${score}/${questions.length}</span>`;
                document.getElementById('options').innerHTML = '';
                this.style.display = 'none';
                document.getElementById('startBtn').style.display = 'inline-block';
                currentQuestion = 0;
                score = 0;
                document.getElementById('scoreValue').textContent = score;
                document.getElementById('totalQuestions').textContent = questions.length;
            }
        });
        
        function showQuestion() {
            const q = questions[currentQuestion];
            document.getElementById('question').textContent = q.question;
            document.getElementById('character').innerHTML = q.svg + `<p>${q.character}</p>`;
            document.getElementById('scoreValue').textContent = score;
            document.getElementById('totalQuestions').textContent = questions.length;
            
            // Shuffle options
            const shuffledOptions = [...q.options].sort(() => Math.random() - 0.5);
            
            const optionsContainer = document.getElementById('options');
            optionsContainer.innerHTML = '';
            
            shuffledOptions.forEach(option => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option';
                optionDiv.textContent = option;
                optionDiv.addEventListener('click', function() {
                    checkAnswer(option, q.correctAnswer);
                });
                optionsContainer.appendChild(optionDiv);
            });
        }
        
        function checkAnswer(selected, correct) {
            const options = document.querySelectorAll('.option');
            options.forEach(option => {
                option.style.pointerEvents = 'none'; // Disable further clicks
                if (option.textContent === correct) {
                    option.classList.add('correct');
                } else if (option.textContent === selected && selected !== correct) {
                    option.classList.add('incorrect');
                }
            });
            
            const feedback = document.getElementById('feedback');
            if (selected === correct) {
                feedback.textContent = 'Correct! Well done!';
                feedback.className = 'feedback correct-feedback';
                score++;
                document.getElementById('scoreValue').textContent = score;
                
                // Highlight correct parts in the answer
                const answerParts = correct.split(' ');
                const pronoun = answerParts[1]; // she/he/they/I
                const verb = answerParts[2]; // is/am/are
                
                feedback.innerHTML += `<div style="margin-top:10px;">"<span class="highlight">${pronoun}</span> <span class="highlight">${verb}</span>" is correct for ${questions[currentQuestion].character}.</div>`;
            } else {
                feedback.textContent = `Oops! The correct answer is: "${correct}"`;
                feedback.className = 'feedback incorrect-feedback';
                
                // Explain why
                let explanation = '';
                if (correct.includes('she') && selected.includes('he')) {
                    explanation = 'Remember, Emma and Katie are girls, so we use "she".';
                } else if (correct.includes('he') && selected.includes('she')) {
                    explanation = 'Remember, Tom is a boy, so we use "he".';
                } else if (correct.includes('they') && selected.includes('he') || selected.includes('she')) {
                    explanation = 'For two people (Billy and Lucy), we use "they".';
                } else if (correct.includes('I') && selected.includes('he') || selected.includes('she') || selected.includes('they')) {
                    explanation = 'When talking about yourself, use "I".';
                } else if (correct.includes('is') && selected.includes('are') || selected.includes('am')) {
                    explanation = 'With "he", "she" or "it", we use "is".';
                } else if (correct.includes('are') && selected.includes('is') || selected.includes('am')) {
                    explanation = 'With "they" or "you", we use "are".';
                } else if (correct.includes('am') && selected.includes('is') || selected.includes('are')) {
                    explanation = 'With "I", we use "am".';
                }
                
                if (explanation) {
                    feedback.innerHTML += `<div style="margin-top:10px;">${explanation}</div>`;
                }
            }
        }
    </script>
</body>
</html>