<!DOCTYPE html>
<html>
<head>
    <title>Holiday Plans</title>
    <style>
        body {
            font-family: 'Open Dyslexic', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        .calendar {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px;
            margin-bottom: 30px;
        }
        .day {
            background-color: white;
            border-radius: 8px;
            padding: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .day-name {
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            color: #e74c3c;
        }
        .activity {
            background-color: #eaf7fd;
            border-radius: 5px;
            padding: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            text-align: center;
        }
        .activity.selected {
            background-color: #a1d8f0;
            font-weight: bold;
        }
        .activities-pool {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-bottom: 20px;
        }
        .activity-option {
            background-color: #d5f5e3;
            border-radius: 5px;
            padding: 10px;
            cursor: grab;
        }
        .sentence-builder {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            text-align: center;
            font-size: 18px;
            min-height: 60px;
        }
        .pronoun-btn {
            background-color: #f39c12;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .dyslexia-font {
            font-size: 18px;
            letter-spacing: 0.05em;
        }
        .visual-prompt {
            text-align: center;
            margin: 20px 0;
        }
        .visual-prompt img {
            width: 100px;
            height: 100px;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-font">
        <h1>My Holiday Plans</h1>
        
        <div class="visual-prompt">
            <svg viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="30" width="180" height="40" rx="5" fill="#f1c40f" stroke="#000" stroke-width="2"/>
                <text x="20" y="60" font-family="Arial" font-size="20" fill="#000">What are you going to do?</text>
            </svg>
        </div>
        
        <div class="activities-pool" id="activitiesPool">
            <div class="activity-option" draggable="true" data-activity="visit the zoo">visit the zoo</div>
            <div class="activity-option" draggable="true" data-activity="help my dad">help my dad</div>
            <div class="activity-option" draggable="true" data-activity="watch TV">watch TV</div>
            <div class="activity-option" draggable="true" data-activity="ride my bike">ride my bike</div>
            <div class="activity-option" draggable="true" data-activity="play football">play football</div>
            <div class="activity-option" draggable="true" data-activity="visit family">visit family</div>
            <div class="activity-option" draggable="true" data-activity="do my homework">do my homework</div>
            <div class="activity-option" draggable="true" data-activity="listen to music">listen to music</div>
        </div>
        
        <div class="calendar">
            <div class="day">
                <div class="day-name">Monday</div>
                <div class="activity" data-day="monday"></div>
                <div class="activity" data-day="monday"></div>
            </div>
            <div class="day">
                <div class="day-name">Tuesday</div>
                <div class="activity" data-day="tuesday"></div>
                <div class="activity" data-day="tuesday"></div>
            </div>
            <div class="day">
                <div class="day-name">Wednesday</div>
                <div class="activity" data-day="wednesday"></div>
                <div class="activity" data-day="wednesday"></div>
            </div>
            <div class="day">
                <div class="day-name">Thursday</div>
                <div class="activity" data-day="thursday"></div>
                <div class="activity" data-day="thursday"></div>
            </div>
            <div class="day">
                <div class="day-name">Friday</div>
                <div class="activity" data-day="friday"></div>
                <div class="activity" data-day="friday"></div>
            </div>
        </div>
        
        <div class="controls">
            <button id="pronounMe">I</button>
            <button id="pronounHe">He</button>
            <button id="pronounShe">She</button>
            <button id="pronounThey">They</button>
        </div>
        
        <div class="sentence-builder" id="sentenceBuilder">
            What are you going to do?
        </div>
        
        <div class="controls">
            <button id="generateBtn">Make Sentence</button>
            <button id="resetBtn">Reset</button>
        </div>
    </div>

    <script>
        let selectedDay = null;
        let selectedActivity = null;
        let selectedPronoun = 'I';
        
        // Make activity options draggable
        document.querySelectorAll('.activity-option').forEach(option => {
            option.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.getAttribute('data-activity'));
            });
        });
        
        // Allow drop on activities
        document.querySelectorAll('.activity').forEach(activity => {
            activity.addEventListener('dragover', function(e) {
                e.preventDefault();
            });
            
            activity.addEventListener('drop', function(e) {
                e.preventDefault();
                const activityText = e.dataTransfer.getData('text/plain');
                this.textContent = activityText;
                this.classList.add('selected');
                this.setAttribute('data-activity', activityText);
                selectedActivity = activityText;
                selectedDay = this.parentElement.querySelector('.day-name').textContent.toLowerCase();
            });
            
            activity.addEventListener('click', function() {
                document.querySelectorAll('.activity').forEach(a => a.classList.remove('selected'));
                this.classList.add('selected');
                selectedActivity = this.getAttribute('data-activity');
                selectedDay = this.parentElement.querySelector('.day-name').textContent.toLowerCase();
            });
        });
        
        // Pronoun selection
        document.getElementById('pronounMe').addEventListener('click', function() {
            selectedPronoun = 'I';
            updatePronounButtons();
        });
        document.getElementById('pronounHe').addEventListener('click', function() {
            selectedPronoun = 'He';
            updatePronounButtons();
        });
        document.getElementById('pronounShe').addEventListener('click', function() {
            selectedPronoun = 'She';
            updatePronounButtons();
        });
        document.getElementById('pronounThey').addEventListener('click', function() {
            selectedPronoun = 'They';
            updatePronounButtons();
        });
        
        function updatePronounButtons() {
            document.querySelectorAll('.controls button').forEach(btn => btn.style.backgroundColor = '#3498db');
            document.getElementById(`pronoun${selectedPronoun}`).style.backgroundColor = '#e67e22';
        }
        
        // Generate sentence
        document.getElementById('generateBtn').addEventListener('click', function() {
            if (!selectedDay || !selectedActivity) {
                document.getElementById('sentenceBuilder').textContent = 'Please select a day and activity first!';
                return;
            }
            
            let sentence;
            if (selectedPronoun === 'I') {
                sentence = `On ${selectedDay}, I am going to ${selectedActivity}.`;
            } else {
                const verb = selectedPronoun === 'They' ? 'are' : 'is';
                sentence = `On ${selectedDay}, ${selectedPronoun} ${verb} going to ${selectedActivity}.`;
            }
            
            document.getElementById('sentenceBuilder').textContent = sentence;
        });
        
        // Reset
        document.getElementById('resetBtn').addEventListener('click', function() {
            document.querySelectorAll('.activity').forEach(a => {
                a.textContent = '';
                a.removeAttribute('data-activity');
                a.classList.remove('selected');
            });
            selectedDay = null;
            selectedActivity = null;
            document.getElementById('sentenceBuilder').textContent = 'What are you going to do?';
        });
    </script>
</body>
</html>