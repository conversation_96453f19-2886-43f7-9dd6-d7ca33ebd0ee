<!DOCTYPE html>
<html>
<head>
    <title>Going To Memory Game</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            background-color: #f0f7f4;
            color: #2b5876;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #e74c3c;
        }
        .game-board {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 30px auto;
            max-width: 600px;
        }
        .card {
            height: 100px;
            background-color: #4a90e2;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 18px;
            color: white;
            text-align: center;
            perspective: 1000px;
            transform-style: preserve-3d;
            transition: transform 0.5s;
            position: relative;
        }
        .card .front, .card .back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
            padding: 10px;
            box-sizing: border-box;
        }
        .card .front {
            background-color: #4a90e2;
            transform: rotateY(180deg);
        }
        .card .back {
            background-color: #3498db;
        }
        .card.flipped {
            transform: rotateY(180deg);
        }
        .card.matched {
            background-color: #2ecc71;
            cursor: default;
        }
        .card .back svg {
            width: 50px;
            height: 50px;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background-color: #c0392b;
        }
        .score {
            text-align: center;
            font-size: 20px;
            margin-bottom: 20px;
        }
        .dyslexia-friendly {
            letter-spacing: 0.05em;
            word-spacing: 0.1em;
        }
        .timer {
            text-align: center;
            font-size: 20px;
            margin-bottom: 10px;
        }
        .celebrate {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: rgba(0,0,0,0.7);
            z-index: 100;
            display: none;
        }
        .celebrate-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-friendly">
        <h1>Memory Match: Going To Sentences</h1>
        
        <div class="timer">
            Time: <span id="time">0</span> seconds
        </div>
        
        <div class="score">
            Matches: <span id="matches">0</span> / 8
        </div>
        
        <div class="game-board" id="gameBoard">
            <!-- Cards will be generated here -->
        </div>
        
        <div class="controls">
            <button id="startBtn">Start Game</button>
            <button id="resetBtn" style="display:none;">Play Again</button>
        </div>
    </div>
    
    <div class="celebrate" id="celebrate">
        <div class="celebrate-content">
            <h2>Congratulations! 🎉</h2>
            <p>You matched all the cards in <span id="finalTime">0</span> seconds!</p>
            <button id="closeCelebrate">Close</button>
        </div>
    </div>

    <script>
        const cardPairs = [
            {
                sentence: "Emma is going to take a camera.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="30" width="60" height="40" rx="5" fill="#555"/>
                    <circle cx="50" cy="50" r="15" fill="#222"/>
                    <circle cx="50" cy="50" r="8" fill="#000"/>
                    <rect x="65" y="25" width="10" height="10" fill="#555"/>
                </svg>`
            },
            {
                sentence: "Tom isn't going to take sun cream.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="30" y="40" width="40" height="50" rx="5" fill="#FFD700"/>
                    <circle cx="50" cy="30" r="15" fill="#FFA500"/>
                </svg>`
            },
            {
                sentence: "Katie is going to take a hairbrush.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="40" y="30" width="10" height="50" fill="#8B4513"/>
                    <line x1="40" y1="30" x2="20" y2="15" stroke="#8B4513" stroke-width="3"/>
                    <line x1="45" y1="30" x2="25" y2="15" stroke="#8B4513" stroke-width="3"/>
                    <line x1="50" y1="30" x2="30" y2="15" stroke="#8B4513" stroke-width="3"/>
                </svg>`
            },
            {
                sentence: "I am going to visit the zoo.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="60" width="60" height="20" fill="#8D6E63"/>
                    <rect x="25" y="40" width="10" height="20" fill="#795548"/>
                    <rect x="65" y="40" width="10" height="20" fill="#795548"/>
                    <circle cx="40" cy="30" r="10" fill="#FFD54F"/>
                    <circle cx="60" cy="30" r="10" fill="#4FC3F7"/>
                </svg>`
            },
            {
                sentence: "They are going to play football.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="20" fill="#FFF" stroke="#000" stroke-width="2"/>
                    <path d="M50,30 L50,70 M30,50 L70,50 M40,40 L60,60 M40,60 L60,40" stroke="#000" stroke-width="2"/>
                </svg>`
            },
            {
                sentence: "She is going to ride her bike.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="30" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                    <circle cx="70" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                    <path d="M30,55 L50,30 L70,55" stroke="#000" stroke-width="3" fill="none"/>
                    <line x1="50" y1="30" x2="50" y2="55" stroke="#000" stroke-width="3"/>
                </svg>`
            },
            {
                sentence: "We are going to watch TV.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="30" width="60" height="40" fill="#424242"/>
                    <rect x="35" y="70" width="30" height="5" fill="#757575"/>
                    <circle cx="30" cy="25" r="5" fill="#E0E0E0"/>
                </svg>`
            },
            {
                sentence: "He is going to help his dad.",
                image: `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="35" cy="40" r="15" fill="#FFD3B6"/>
                    <circle cx="65" cy="40" r="15" fill="#D4A5A5"/>
                    <path d="M35,55 L35,80 M25,65 L45,65" stroke="#000" stroke-width="2" fill="none"/>
                    <path d="M65,55 L65,80 M55,65 L75,65" stroke="#000" stroke-width="2" fill="none"/>
                    <path d="M35,50 L50,30 L65,50" stroke="#000" stroke-width="2" fill="none"/>
                </svg>`
            }
        ];
        
        let cards = [];
        let flippedCards = [];
        let matchedPairs = 0;
        let gameStarted = false;
        let startTime = 0;
        let timerInterval;
        
        // Initialize game
        function initGame() {
            const gameBoard = document.getElementById('gameBoard');
            gameBoard.innerHTML = '';
            flippedCards = [];
            matchedPairs = 0;
            document.getElementById('matches').textContent = matchedPairs;
            clearInterval(timerInterval);
            document.getElementById('time').textContent = '0';
            
            // Create doubled card array and shuffle
            let gameCards = [];
            cardPairs.forEach((pair, index) => {
                gameCards.push({
                    id: index * 2,
                    type: 'sentence',
                    content: pair.sentence,
                    pairId: index
                });
                gameCards.push({
                    id: index * 2 + 1,
                    type: 'image',
                    content: pair.image,
                    pairId: index
                });
            });
            
            // Shuffle cards
            cards = gameCards.sort(() => Math.random() - 0.5);
            
            // Create card elements
            cards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = 'card';
                cardElement.dataset.id = card.id;
                cardElement.dataset.pairId = card.pairId;
                
                const front = document.createElement('div');
                front.className = 'front';
                if (card.type === 'sentence') {
                    front.textContent = card.content;
                    // Highlight pronouns and verbs
                    const parts = card.content.split(' ');
                    const pronoun = parts[0];
                    const verb = parts[1];
                    front.innerHTML = card.content.replace(
                        `${pronoun} ${verb}`, 
                        `<span style="background-color:#f9e79f;padding:2px;">${pronoun}</span> <span style="background-color:#a9dfbf;padding:2px;">${verb}</span>`
                    );
                } else {
                    front.innerHTML = card.content;
                }
                
                const back = document.createElement('div');
                back.className = 'back';
                back.innerHTML = `<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="40" fill="#3498db"/>
                    <text x="50" y="55" font-family="Arial" font-size="30" fill="white" text-anchor="middle">?</text>
                </svg>`;
                
                cardElement.appendChild(front);
                cardElement.appendChild(back);
                cardElement.addEventListener('click', flipCard);
                gameBoard.appendChild(cardElement);
            });
        }
        
        // Flip card
        function flipCard() {
            if (!gameStarted) return;
            if (flippedCards.length >= 2) return;
            if (this.classList.contains('flipped') || this.classList.contains('matched')) return;
            
            this.classList.add('flipped');
            flippedCards.push(this);
            
            if (flippedCards.length === 2) {
                checkForMatch();
            }
        }
        
        // Check for match
        function checkForMatch() {
            const card1 = flippedCards[0];
            const card2 = flippedCards[1];
            
            if (card1.dataset.pairId === card2.dataset.pairId) {
                // Match found
                card1.classList.add('matched');
                card2.classList.add('matched');
                flippedCards = [];
                matchedPairs++;
                document.getElementById('matches').textContent = matchedPairs;
                
                // Check if game is complete
                if (matchedPairs === cardPairs.length) {
                    clearInterval(timerInterval);
                    setTimeout(() => {
                        document.getElementById('finalTime').textContent = document.getElementById('time').textContent;
                        document.getElementById('celebrate').style.display = 'flex';
                    }, 500);
                }
            } else {
                // No match
                setTimeout(() => {
                    card1.classList.remove('flipped');
                    card2.classList.remove('flipped');
                    flippedCards = [];
                }, 1000);
            }
        }
        
        // Start game
        document.getElementById('startBtn').addEventListener('click', function() {
            initGame();
            gameStarted = true;
            this.style.display = 'none';
            document.getElementById('resetBtn').style.display = 'inline-block';
            startTime = Date.now();
            timerInterval = setInterval(updateTimer, 1000);
        });
        
        // Reset game
        document.getElementById('resetBtn').addEventListener('click', function() {
            initGame();
            gameStarted = true;
            startTime = Date.now();
            clearInterval(timerInterval);
            timerInterval = setInterval(updateTimer, 1000);
        });
        
        // Close celebration
        document.getElementById('closeCelebrate').addEventListener('click', function() {
            document.getElementById('celebrate').style.display = 'none';
        });
        
        // Update timer
        function updateTimer() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('time').textContent = elapsed;
        }
    </script>
</body>
</html>