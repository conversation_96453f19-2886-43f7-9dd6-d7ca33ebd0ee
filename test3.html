<!DOCTYPE html>
<html>
<head>
    <title>Going To Sentence Builder</title>
    <style>
        body {
            font-family: 'Comic Sans MS', cursive;
            background-color: #e8f4f8;
            color: #2c3e50;
            line-height: 1.8;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #e74c3c;
        }
        .sentence-parts {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-bottom: 30px;
        }
        .word-card {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.1);
            cursor: pointer;
            text-align: center;
            font-size: 18px;
            min-width: 80px;
        }
        .word-card:hover {
            background-color: #d6eaf8;
        }
        .word-card.pronoun {
            background-color: #f9e79f;
        }
        .word-card.verb {
            background-color: #a9dfbf;
        }
        .word-card.activity {
            background-color: #d2b4de;
        }
        .sentence-area {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            min-height: 100px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            align-items: center;
        }
        .sentence-word {
            background-color: #eaf2f8;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 18px;
        }
        .visual-prompt {
            text-align: center;
            margin: 20px 0;
        }
        .visual-prompt svg {
            width: 150px;
            height: 150px;
        }
        .controls {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 0 10px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .dyslexia-friendly {
            letter-spacing: 0.05em;
            word-spacing: 0.1em;
        }
        .image-option {
            display: inline-block;
            text-align: center;
            margin: 10px;
            cursor: pointer;
        }
        .image-option svg {
            width: 80px;
            height: 80px;
            margin-bottom: 5px;
        }
        .image-option.selected {
            background-color: #d5f5e3;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container dyslexia-friendly">
        <h1>What are they going to do?</h1>
        
        <div class="visual-prompt">
            <svg viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="30" width="180" height="40" rx="5" fill="#f1c40f" stroke="#000" stroke-width="2"/>
                <text x="20" y="60" font-family="Arial" font-size="20" fill="#000">Build a sentence with "going to"</text>
            </svg>
        </div>
        
        <h2>Choose a person:</h2>
        <div class="sentence-parts" id="peopleOptions">
            <div class="image-option" data-name="Emma" data-pronoun="she">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFD3B6"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Emma</p>
            </div>
            <div class="image-option" data-name="Tom" data-pronoun="he">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#D4A5A5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,55 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Tom</p>
            </div>
            <div class="image-option" data-name="Katie" data-pronoun="she">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#FFAAA5"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                </svg>
                <p>Katie</p>
            </div>
            <div class="image-option" data-name="I" data-pronoun="I">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="40" r="20" fill="#A5D6A7"/>
                    <path d="M50,60 L50,90 M30,70 L70,70" stroke="#000" stroke-width="3" fill="none"/>
                    <circle cx="40" cy="35" r="3" fill="#000"/>
                    <circle cx="60" cy="35" r="3" fill="#000"/>
                    <path d="M40,45 Q50,50 60,45" stroke="#000" stroke-width="2" fill="none"/>
                    <text x="45" y="25" font-family="Arial" font-size="15" fill="#000">You</text>
                </svg>
                <p>Me</p>
            </div>
        </div>
        
        <h2>Choose an activity:</h2>
        <div class="sentence-parts" id="activityOptions">
            <div class="image-option" data-activity="visit the zoo">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="60" width="60" height="20" fill="#8D6E63"/>
                    <rect x="25" y="40" width="10" height="20" fill="#795548"/>
                    <rect x="65" y="40" width="10" height="20" fill="#795548"/>
                    <circle cx="40" cy="30" r="10" fill="#FFD54F"/>
                    <circle cx="60" cy="30" r="10" fill="#4FC3F7"/>
                </svg>
                <p>visit the zoo</p>
            </div>
            <div class="image-option" data-activity="ride my bike">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="30" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                    <circle cx="70" cy="70" r="15" fill="#BDBDBD" stroke="#000" stroke-width="2"/>
                    <path d="M30,55 L50,30 L70,55" stroke="#000" stroke-width="3" fill="none"/>
                    <line x1="50" y1="30" x2="50" y2="55" stroke="#000" stroke-width="3"/>
                </svg>
                <p>ride my bike</p>
            </div>
            <div class="image-option" data-activity="play football">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="50" cy="50" r="20" fill="#FFF" stroke="#000" stroke-width="2"/>
                    <path d="M50,30 L50,70 M30,50 L70,50 M40,40 L60,60 M40,60 L60,40" stroke="#000" stroke-width="2"/>
                </svg>
                <p>play football</p>
            </div>
            <div class="image-option" data-activity="watch TV">
                <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <rect x="20" y="30" width="60" height="40" fill="#424242"/>
                    <rect x="35" y="70" width="30" height="5" fill="#757575"/>
                    <circle cx="30" cy="25" r="5" fill="#E0E0E0"/>
                </svg>
                <p>watch TV</p>
            </div>
        </div>
        
        <h2>Your sentence:</h2>
        <div class="sentence-area" id="sentenceArea">
            <!-- Sentence will be built here -->
        </div>
        
        <div class="controls">
            <button id="speakBtn">Speak Sentence</button>
            <button id="resetBtn">Start Again</button>
        </div>
    </div>

    <script>
        let selectedPerson = null;
        let selectedActivity = null;
        
        // Select person
        document.querySelectorAll('#peopleOptions .image-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('#peopleOptions .image-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedPerson = {
                    name: this.getAttribute('data-name'),
                    pronoun: this.getAttribute('data-pronoun')
                };
                buildSentence();
            });
        });
        
        // Select activity
        document.querySelectorAll('#activityOptions .image-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('#activityOptions .image-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedActivity = this.getAttribute('data-activity');
                buildSentence();
            });
        });
        
        // Build the sentence
        function buildSentence() {
            const sentenceArea = document.getElementById('sentenceArea');
            sentenceArea.innerHTML = '';
            
            if (!selectedPerson || !selectedActivity) return;
            
            const words = [];
            
            if (selectedPerson.name === 'I') {
                words.push({text: 'I', class: 'pronoun'});
                words.push({text: 'am', class: 'verb'});
            } else {
                words.push({text: selectedPerson.name, class: 'pronoun'});
                words.push({text: selectedPerson.pronoun === 'they' ? 'are' : 'is', class: 'verb'});
            }
            
            words.push({text: 'going to', class: 'verb'});
            words.push({text: selectedActivity, class: 'activity'});
            
            words.forEach(word => {
                const wordSpan = document.createElement('span');
                wordSpan.className = `sentence-word ${word.class}`;
                wordSpan.textContent = word.text;
                sentenceArea.appendChild(wordSpan);
            });
            
            // Add question mark if it's a question
            if (Math.random() > 0.7) { // 30% chance to make it a question
                const questionMark = document.createElement('span');
                questionMark.className = 'sentence-word';
                questionMark.textContent = '?';
                sentenceArea.insertBefore(document.createElement('span').textContent = ' ', sentenceArea.firstChild);
                sentenceArea.insertBefore(document.createElement('span').textContent = 'Are', sentenceArea.firstChild);
                if (selectedPerson.name !== 'I') {
                    sentenceArea.children[1].textContent = selectedPerson.pronoun;
                }
            } else {
                const period = document.createElement('span');
                period.className = 'sentence-word';
                period.textContent = '.';
                sentenceArea.appendChild(period);
            }
        }
        
        // Speak sentence
        document.getElementById('speakBtn').addEventListener('click', function() {
            if (!selectedPerson || !selectedActivity) return;
            
            const sentence = document.getElementById('sentenceArea').textContent;
            if (speechSynthesis) {
                const utterance = new SpeechSynthesisUtterance(sentence);
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            }
        });
        
        // Reset
        document.getElementById('resetBtn').addEventListener('click', function() {
            document.querySelectorAll('.image-option').forEach(opt => opt.classList.remove('selected'));
            selectedPerson = null;
            selectedActivity = null;
            document.getElementById('sentenceArea').innerHTML = '';
        });
    </script>
</body>
</html>